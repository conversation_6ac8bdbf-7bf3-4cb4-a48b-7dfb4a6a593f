/**
 * إعدادات تحسين محركات البحث - منصة التعليم المغربي
 * SEO Configuration - Moroccan Education Platform
 */

const seoConfig = {
  // Site Information
  siteName: 'منصة التعليم المغربي',
  siteNameEn: 'Moroccan Education Platform',
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://www.tolabi.net',
  
  // Default Meta Tags
  defaultTitle: 'منصة التعليم المغربي - تمارين ودروس تفاعلية',
  defaultDescription: 'منصة تعليمية متكاملة للطلاب في المغرب - دروس وتمارين تفاعلية لجميع المراحل الدراسية وفقاً للمناهج المغربية',
  
  // Keywords
  keywords: [
    'تعليم مغربي',
    'دروس تفاعلية',
    'تمارين تعليمية',
    'مناهج مغربية',
    'طلاب مغاربة',
    'تعليم ابتدائي المغرب',
    'تعليم إعدادي المغرب',
    'تعليم ثانوي المغرب',
    'باكالوريا مغربية',
    'امتحانات مغربية',
    'ملخصات دروس',
    'فروض منزلية',
    'تعليم إلكتروني المغرب',
    'منصة تعليمية مغربية',
    'مراجعة دروس',
    'جذع مشترك',
    'أولى باك',
    'ثانية باك'
  ],

  // Moroccan Education Subjects
  subjects: [
    'الرياضيات',
    'العلوم الفيزيائية',
    'علوم الحياة والأرض',
    'اللغة العربية',
    'اللغة الفرنسية',
    'اللغة الإنجليزية',
    'التاريخ والجغرافيا',
    'التربية الإسلامية',
    'الفلسفة',
    'الاقتصاد والتدبير',
    'المحاسبة والرياضيات المالية',
    'القانون',
    'الإعلاميات'
  ],

  // Educational Levels in Morocco
  levels: [
    {
      id: 'primary',
      name: 'التعليم الابتدائي',
      nameEn: 'Primary Education',
      grades: ['الأول ابتدائي', 'الثاني ابتدائي', 'الثالث ابتدائي', 'الرابع ابتدائي', 'الخامس ابتدائي', 'السادس ابتدائي']
    },
    {
      id: 'middle',
      name: 'التعليم الإعدادي',
      nameEn: 'Middle School',
      grades: ['الأولى إعدادي', 'الثانية إعدادي', 'الثالثة إعدادي']
    },
    {
      id: 'high',
      name: 'التعليم الثانوي',
      nameEn: 'High School',
      grades: ['جذع مشترك علمي', 'جذع مشترك أدبي', 'الأولى باك علوم', 'الأولى باك آداب', 'الثانية باك علوم', 'الثانية باك آداب']
    }
  ],

  // Social Media
  social: {
    twitter: '@MoroccanEducation',
    facebook: 'MoroccanEducationPlatform',
    instagram: '@moroccan_education',
    youtube: '@MoroccanEducationPlatform'
  },

  // Organization Schema
  organization: {
    '@type': 'EducationalOrganization',
    name: 'منصة التعليم المغربي',
    alternateName: 'Moroccan Education Platform',
    description: 'منصة تعليمية متكاملة للطلاب في المغرب',
    foundingDate: '2024',
    areaServed: {
      '@type': 'Country',
      name: 'المغرب',
      alternateName: 'Morocco'
    },
    audience: {
      '@type': 'EducationalAudience',
      educationalRole: 'student',
      audienceType: 'طلاب المراحل الدراسية في المغرب'
    },
    inLanguage: ['ar', 'fr'],
    availableLanguage: ['ar', 'fr']
  },

  // Open Graph
  openGraph: {
    type: 'website',
    locale: 'ar_MA',
    siteName: 'منصة التعليم المغربي',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'منصة التعليم المغربي - تمارين ودروس تفاعلية'
      }
    ]
  },

  // Twitter
  twitter: {
    handle: '@MoroccanEducation',
    site: '@MoroccanEducation',
    cardType: 'summary_large_image'
  },

  // Robots
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1
    }
  },

  // Additional Meta Tags
  additionalMetaTags: [
    {
      name: 'application-name',
      content: 'منصة التعليم المغربي'
    },
    {
      name: 'apple-mobile-web-app-title',
      content: 'منصة التعليم المغربي'
    },
    {
      name: 'format-detection',
      content: 'telephone=no'
    },
    {
      name: 'mobile-web-app-capable',
      content: 'yes'
    },
    {
      name: 'msapplication-TileColor',
      content: '#2563eb'
    },
    {
      name: 'theme-color',
      content: '#2563eb'
    }
  ],

  // Verification
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    bing: process.env.BING_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION
  }
};

module.exports = seoConfig;
