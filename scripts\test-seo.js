#!/usr/bin/env node

/**
 * اختبار تحسينات محركات البحث (SEO) - منصة التعليم المغربي
 * SEO Testing Script - Moroccan Education Platform
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 بدء اختبار تحسينات محركات البحث...');
console.log('Starting SEO optimization tests...\n');

// Test results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

function addTest(name, status, message) {
  results.tests.push({ name, status, message });
  if (status === 'PASS') results.passed++;
  else if (status === 'FAIL') results.failed++;
  else if (status === 'WARN') results.warnings++;
}

function checkFileExists(filePath, description) {
  const fullPath = path.join(path.dirname(__dirname), filePath);
  if (fs.existsSync(fullPath)) {
    addTest(`File: ${filePath}`, 'PASS', `${description} موجود`);
    return true;
  } else {
    addTest(`File: ${filePath}`, 'FAIL', `${description} غير موجود`);
    return false;
  }
}

function checkFileContent(filePath, searchText, description) {
  const fullPath = path.join(path.dirname(__dirname), filePath);
  if (fs.existsSync(fullPath)) {
    const content = fs.readFileSync(fullPath, 'utf8');
    if (content.includes(searchText)) {
      addTest(`Content: ${filePath}`, 'PASS', `${description} موجود في الملف`);
      return true;
    } else {
      addTest(`Content: ${filePath}`, 'FAIL', `${description} غير موجود في الملف`);
      return false;
    }
  } else {
    addTest(`Content: ${filePath}`, 'FAIL', `الملف ${filePath} غير موجود`);
    return false;
  }
}

// Test 1: Check essential SEO files
console.log('📁 اختبار الملفات الأساسية...');
checkFileExists('app/sitemap.ts', 'خريطة الموقع');
checkFileExists('public/robots.txt', 'ملف robots.txt');
checkFileExists('public/manifest.json', 'ملف manifest.json');
checkFileExists('lib/seo.ts', 'مكتبة SEO');

// Test 2: Check SEO components
console.log('\n🧩 اختبار مكونات SEO...');
checkFileExists('components/Breadcrumb.tsx', 'مكون مسارات التنقل');
checkFileExists('components/Analytics.tsx', 'مكون التحليلات');
checkFileExists('components/OptimizedImage.tsx', 'مكون الصور المحسنة');
checkFileExists('components/PerformanceOptimizer.tsx', 'مكون تحسين الأداء');

// Test 3: Check layout updates
console.log('\n📄 اختبار تحديثات Layout...');
checkFileContent('app/layout.tsx', 'منصة التعليم المغربي', 'العنوان المغربي');
checkFileContent('app/layout.tsx', 'ar_MA', 'اللغة المغربية');
checkFileContent('app/layout.tsx', 'EducationalOrganization', 'البيانات المنظمة');
checkFileContent('app/layout.tsx', 'GoogleAnalytics', 'Google Analytics');

// Test 4: Check robots.txt content
console.log('\n🤖 اختبار محتوى robots.txt...');
checkFileContent('public/robots.txt', 'منصة التعليم المغربي', 'العنوان المغربي في robots.txt');
checkFileContent('public/robots.txt', 'Sitemap:', 'رابط خريطة الموقع');
checkFileContent('public/robots.txt', 'Disallow: /admin/', 'حظر المجلدات الإدارية');

// Test 5: Check manifest.json content
console.log('\n📱 اختبار محتوى manifest.json...');
checkFileContent('public/manifest.json', 'منصة التعليم المغربي', 'اسم التطبيق المغربي');
checkFileContent('public/manifest.json', 'المغرب', 'المحتوى المغربي');
checkFileContent('public/manifest.json', 'standalone', 'وضع التطبيق المستقل');

// Test 6: Check page metadata
console.log('\n📋 اختبار metadata الصفحات...');
checkFileContent('app/levels/page.tsx', 'منصة التعليم المغربي', 'عنوان صفحة المستويات');
checkFileContent('app/about/page.tsx', 'منصة التعليم المغربي', 'عنوان صفحة حول المنصة');
checkFileContent('app/about/page.tsx', 'BreadcrumbStructuredData', 'البيانات المنظمة للتنقل');

// Test 7: Check SEO library
console.log('\n📚 اختبار مكتبة SEO...');
checkFileContent('lib/seo.ts', 'منصة التعليم المغربي', 'اسم الموقع في مكتبة SEO');
checkFileContent('lib/seo.ts', 'ar_MA', 'اللغة المغربية في SEO');
checkFileContent('lib/seo.ts', 'generateMetadata', 'دالة توليد metadata');

// Test 8: Check environment variables
console.log('\n🔧 اختبار متغيرات البيئة...');
checkFileExists('.env.example', 'ملف متغيرات البيئة المثال');
checkFileContent('.env.example', 'NEXT_PUBLIC_GA_ID', 'متغير Google Analytics');
checkFileContent('.env.example', 'منصة التعليم المغربي', 'اسم الموقع في متغيرات البيئة');

// Test 9: Check icons and images
console.log('\n🖼️ اختبار الأيقونات والصور...');
checkFileExists('public/favicon.ico', 'أيقونة الموقع');
checkFileExists('public/favicon.svg', 'أيقونة SVG');
checkFileExists('public/og-image.svg', 'صورة Open Graph');
checkFileContent('public/og-image.svg', 'منصة التعليم المغربي', 'العنوان في صورة OG');

// Test 10: Check service worker
console.log('\n⚙️ اختبار Service Worker...');
checkFileExists('public/sw.js', 'Service Worker');
checkFileContent('public/sw.js', 'arab-edu-v1', 'اسم Cache');
checkFileContent('public/sw.js', 'offline', 'دعم وضع عدم الاتصال');

// Display results
console.log('\n' + '='.repeat(60));
console.log('📊 نتائج الاختبار / Test Results');
console.log('='.repeat(60));

results.tests.forEach(test => {
  const icon = test.status === 'PASS' ? '✅' : test.status === 'FAIL' ? '❌' : '⚠️';
  console.log(`${icon} ${test.name}: ${test.message}`);
});

console.log('\n' + '='.repeat(60));
console.log(`📈 الملخص / Summary:`);
console.log(`✅ نجح: ${results.passed} اختبار`);
console.log(`❌ فشل: ${results.failed} اختبار`);
console.log(`⚠️ تحذيرات: ${results.warnings} اختبار`);
console.log(`📊 المجموع: ${results.tests.length} اختبار`);

const successRate = ((results.passed / results.tests.length) * 100).toFixed(1);
console.log(`🎯 معدل النجاح: ${successRate}%`);

if (results.failed === 0) {
  console.log('\n🎉 تهانينا! جميع اختبارات SEO نجحت!');
  console.log('🚀 الموقع جاهز لمحركات البحث!');
} else {
  console.log('\n⚠️ يرجى إصلاح الأخطاء المذكورة أعلاه');
  console.log('📝 راجع دليل SEO للحصول على المساعدة');
}

console.log('\n📖 للمزيد من المعلومات، راجع: SEO_IMPLEMENTATION_GUIDE.md');

// Exit with appropriate code
process.exit(results.failed > 0 ? 1 : 0);
