#!/usr/bin/env node

/**
 * سكريبت نقل بيانات الامتحانات إلى ملخص الدروس
 * Migration Script: Convert Exams to Lesson Summaries
 *
 * تشغيل: node scripts/migrate-exams-to-summaries.mjs
 */

import { createClient } from '@supabase/supabase-js';

// إعدادات Supabase
const SUPABASE_URL = "https://ckjjqlbzflnxolflixkq.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNrampxbGJ6ZmxueG9sZmxpeGtxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQzNjcyOTQsImV4cCI6MjA0OTk0MzI5NH0.Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7E";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function migrateExamsToSummaries() {
  console.log("🚀 بدء عملية نقل الامتحانات إلى ملخص الدروس...");

  try {
    // 1. التحقق من وجود جدول exams والحصول على البيانات
    console.log("📊 فحص البيانات الموجودة...");

    const { data: examsData, error: examsError } = await supabase
      .from('exams')
      .select('*');

    if (examsError) {
      console.error("❌ خطأ في قراءة جدول exams:", examsError);
      return;
    }

    console.log(`📋 تم العثور على ${examsData?.length || 0} عنصر في جدول الامتحانات`);

    if (!examsData || examsData.length === 0) {
      console.log("ℹ️ لا توجد بيانات امتحانات للنقل");
      return;
    }

    // 2. التحقق من وجود جدول summaries
    console.log("🔍 التحقق من جدول summaries...");

    const { error: summariesError } = await supabase
      .from('summaries')
      .select('id')
      .limit(1);

    if (summariesError) {
      console.error("❌ جدول summaries غير موجود. يرجى تشغيل السكريبت SQL أولاً:", summariesError);
      console.log("💡 قم بتشغيل الملف: scripts/migrate-exams-to-summaries.sql في Supabase SQL Editor");
      return;
    }

    // 3. نقل البيانات من exams إلى summaries
    console.log("📦 نقل البيانات إلى جدول summaries...");

    // تحويل البيانات للتنسيق المطلوب (الحقول الأساسية فقط للملخصات)
    const summariesData = examsData.map(exam => ({
      id: exam.id,
      lesson_id: exam.lesson_id,
      hint: exam.hint,
      exercise_image_url: exam.exercise_image_url
      // ملاحظة: تم إزالة solution_image_url وباقي الحقول لأن ملخص الدروس لا يحتاج حلول
    }));

    // إدراج البيانات في جدول summaries
    const { error: insertError } = await supabase
      .from('summaries')
      .upsert(summariesData, {
        onConflict: 'id',
        ignoreDuplicates: false
      });

    if (insertError) {
      console.error("❌ خطأ في إدراج البيانات في جدول summaries:", insertError);
      return;
    }

    console.log(`✅ تم نقل ${summariesData.length} عنصر إلى جدول summaries`);

    // 4. تحديث نوع المحتوى في جدول lessons
    console.log("🔄 تحديث نوع المحتوى في جدول lessons...");

    const { error: updateError } = await supabase
      .from('lessons')
      .update({ content_type: 'summary' })
      .eq('content_type', 'exam');

    if (updateError) {
      console.error("❌ خطأ في تحديث نوع المحتوى:", updateError);
      return;
    }

    console.log("✅ تم تحديث نوع المحتوى من 'exam' إلى 'summary'");

    // 5. التحقق من النتائج
    console.log("🔍 التحقق من النتائج...");

    const { data: finalSummaries, error: finalError } = await supabase
      .from('summaries')
      .select('*');

    if (finalError) {
      console.error("❌ خطأ في قراءة النتائج النهائية:", finalError);
      return;
    }

    const { data: updatedLessons, error: lessonsError } = await supabase
      .from('lessons')
      .select('id, title, content_type')
      .eq('content_type', 'summary');

    if (lessonsError) {
      console.error("❌ خطأ في قراءة الدروس المحدثة:", lessonsError);
      return;
    }

    // 6. عرض النتائج النهائية
    console.log("\n📊 النتائج النهائية:");
    console.log(`✅ إجمالي عناصر ملخص الدروس: ${finalSummaries?.length || 0}`);
    console.log(`✅ إجمالي الدروس المحدثة: ${updatedLessons?.length || 0}`);

    if (updatedLessons && updatedLessons.length > 0) {
      console.log("\n📚 الدروس التي تم تحويلها إلى ملخص:");
      updatedLessons.forEach((lesson, index) => {
        console.log(`${index + 1}. ${lesson.title} (${lesson.id})`);
      });
    }

    console.log("\n🎉 تم إكمال عملية النقل بنجاح!");
    console.log("\n💡 ملاحظة: يمكنك الآن حذف جدول 'exams' إذا كنت متأكداً من نجاح النقل");
    console.log("   للحذف: DROP TABLE IF EXISTS public.exams;");

  } catch (error) {
    console.error("❌ خطأ عام في عملية النقل:", error);
  }
}

// تشغيل السكريبت
migrateExamsToSummaries();
