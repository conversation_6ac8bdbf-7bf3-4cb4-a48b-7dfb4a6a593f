import { Metadata } from 'next';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Breadcrumb, BreadcrumbStructuredData } from '@/components/Breadcrumb';
import { BookOpen, Book, Users, CheckCircle } from 'lucide-react';

export const metadata: Metadata = {
  title: 'عن المنصة',
  description: 'تعرف على منصة التعليم المغربي للتعليم الرقمي المدرسي في المغرب، رؤيتنا ومهمتنا وما يميزنا. منصة تعليمية شاملة تقدم دروساً وتمارين تفاعلية لجميع المراحل الدراسية وفقاً للمناهج المغربية',
  keywords: [
    'عن المنصة',
    'منصة التعليم المغربي',
    'تعليم إلكتروني المغرب',
    'دروس تفاعلية مغربية',
    'مناهج مغربية',
    'تعليم مغربي',
    'رؤية المنصة',
    'مهمة تعليمية',
    'تمارين تعليمية مغربية',
    'ملخصات دروس مغربية',
    'باكالوريا مغربية',
    'جذع مشترك'
  ],
  openGraph: {
    title: 'عن المنصة',
    description: 'تعرف على منصة التعليم المغربي للتعليم الرقمي المدرسي في المغرب، رؤيتنا ومهمتنا وما يميزنا',
    type: 'website',
    url: 'https://tolabi.net/about',
  },
  alternates: {
    canonical: '/about',
  },
  robots: {
    index: true,
    follow: true,
  },
};

const breadcrumbItems = [
  { label: 'عن المنصة', current: true }
];

export default function AboutPage() {
  return (
    <>
      <BreadcrumbStructuredData items={breadcrumbItems} />

      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "AboutPage",
            "name": "عن منصة التعليم المغربي",
            "description": "صفحة تعريفية بمنصة التعليم المغربي ورؤيتها ومهمتها",
            "url": "https://tolabi.net/about",
            "mainEntity": {
              "@type": "EducationalOrganization",
              "name": "منصة التعليم المغربي",
              "description": "منصة تعليمية متكاملة للطلاب في المغرب",
              "url": "https://tolabi.net",
              "foundingDate": "2024",
              "mission": "توفير محتوى تعليمي عالي الجودة، يشمل الدروس، التمارين، الملخصات، والفروض وفقاً للمناهج المغربية",
              "educationalCredentialAwarded": "شهادة إتمام",
              "areaServed": {
                "@type": "Country",
                "name": "المغرب",
                "alternateName": "Morocco"
              },
              "teaches": [
                "الرياضيات",
                "العلوم الفيزيائية",
                "علوم الحياة والأرض",
                "اللغة العربية",
                "اللغة الفرنسية",
                "التاريخ والجغرافيا",
                "التربية الإسلامية",
                "الفلسفة"
              ],
              "audience": {
                "@type": "EducationalAudience",
                "educationalRole": "student",
                "audienceType": "طلاب المراحل الدراسية في المغرب من الابتدائي إلى الباكالوريا",
                "geographicArea": {
                  "@type": "Country",
                  "name": "المغرب"
                }
              }
            }
          })
        }}
      />

      <div className="min-h-screen flex flex-col">
        <Header />

        <div className="container mx-auto px-4 py-10 arabic-text">
          {/* Breadcrumb Navigation */}
          <Breadcrumb items={breadcrumbItems} className="mb-6" />

          <h1 className="text-4xl font-bold mb-8 text-center text-primary">عن منصة التعليم المغربي</h1>

        <div className="max-w-4xl mx-auto mb-10">
          <p className="text-xl mb-8 arabic-paragraph leading-relaxed">
            منصة التعليم المغربي توفر دروسًا وملخصات وتمارين تفاعلية لجميع المراحل الدراسية من الابتدائي إلى الباكالوريا، متوافقة مع المناهج المغربية الرسمية. تهدف المنصة إلى تسهيل التعلم الذاتي وتقديم محتوى منظم وسهل الوصول للطلاب المغاربة.
          </p>

          <h2 className="text-3xl font-bold mt-12 mb-6 arabic-heading text-primary">رؤيتنا</h2>
          <p className="mb-8 arabic-paragraph text-lg leading-relaxed">
            أن نكون المرجع الأول للتعلم الرقمي المدرسي في المغرب، عبر تقديم تجربة تعليمية حديثة وفعالة لجميع التلاميذ المغاربة وفقاً للمناهج المغربية المعتمدة.
          </p>

          <h2 className="text-3xl font-bold mt-12 mb-6 arabic-heading text-primary">مهمتنا</h2>
          <p className="mb-8 arabic-paragraph text-lg leading-relaxed">
            توفير محتوى تعليمي عالي الجودة متوافق مع المناهج المغربية، يشمل الدروس، التمارين، الملخصات، والفروض، مع حلول مفصلة وتلميحات، لمساعدة التلاميذ المغاربة على الفهم والتطبيق والنجاح في امتحاناتهم.
          </p>

          <h2 className="text-3xl font-bold mt-12 mb-8 arabic-heading text-primary">ما يميزنا</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            <div className="bg-card text-card-foreground p-8 rounded-xl shadow-lg border border-primary/20 hover:border-primary/40 transition-all duration-300">
              <div className="flex items-center mb-6">
                <BookOpen className="w-8 h-8 text-primary ml-4" />
                <h3 className="text-2xl font-bold arabic-heading">محتوى تعليمي منظم</h3>
              </div>
              <p className="text-foreground arabic-paragraph text-lg leading-relaxed">
                دروس وملخصات وتمارين تغطي جميع المستويات الدراسية، مع تصنيف واضح حسب السنوات والمواد.
              </p>
            </div>

            <div className="bg-card text-card-foreground p-8 rounded-xl shadow-lg border border-primary/20 hover:border-primary/40 transition-all duration-300">
              <div className="flex items-center mb-6">
                <Book className="w-8 h-8 text-primary ml-4" />
                <h3 className="text-2xl font-bold arabic-heading">تمارين وحلول</h3>
              </div>
              <p className="text-foreground arabic-paragraph text-lg leading-relaxed">
                نوفر تمارين مع حلول مفصلة، لمساعدة التلاميذ على الفهم والتطبيق الذاتي.
              </p>
            </div>

            <div className="bg-card text-card-foreground p-8 rounded-xl shadow-lg border border-primary/20 hover:border-primary/40 transition-all duration-300">
              <div className="flex items-center mb-6">
                <Users className="w-8 h-8 text-primary ml-4" />
                <h3 className="text-2xl font-bold arabic-heading">تجربة مناسبة للجميع</h3>
              </div>
              <p className="text-foreground arabic-paragraph text-lg leading-relaxed">
                المنصة تدعم جميع المستويات الدراسية وتتيح سهولة التنقل بين المواد والمراحل.
              </p>
            </div>

            <div className="bg-card text-card-foreground p-8 rounded-xl shadow-lg border border-primary/20 hover:border-primary/40 transition-all duration-300">
              <div className="flex items-center mb-6">
                <CheckCircle className="w-8 h-8 text-primary ml-4" />
                <h3 className="text-2xl font-bold arabic-heading">متابعة التقدم</h3>
              </div>
              <p className="text-foreground arabic-paragraph text-lg leading-relaxed">
                تتبع التقدم في الدروس والتمارين، مع إمكانية مراجعة الأداء في كل مادة.
              </p>
            </div>
          </div>
        </div>
      </div>

        <div className="mt-auto">
          <Footer />
        </div>
      </div>
    </>
  );
}
