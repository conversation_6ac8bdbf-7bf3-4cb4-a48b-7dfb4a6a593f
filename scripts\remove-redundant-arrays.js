#!/usr/bin/env node

/**
 * سكريبت إزالة المصفوفات المكررة من قاعدة البيانات
 * Script to Remove Redundant Arrays from Database Tables
 * 
 * هذا السكريبت يزيل الحقول المكررة التي لم تعد مستخدمة في التطبيق:
 * - levels.years
 * - years.subjects  
 * - subjects.lessons
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// تحميل متغيرات البيئة
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ خطأ: متغيرات البيئة مفقودة');
  console.error('تأكد من وجود VITE_SUPABASE_URL و VITE_SUPABASE_ANON_KEY في ملف .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function removeRedundantArrays() {
  console.log('🚀 بدء عملية إزالة المصفوفات المكررة...\n');

  try {
    // 1. عرض هيكل الجداول قبل التحديث
    console.log('📊 هيكل الجداول قبل التحديث:');
    await showTableStructure();

    // 2. تأكيد من المستخدم
    console.log('\n⚠️  تحذير: هذا السكريبت سيزيل الحقول التالية من قاعدة البيانات:');
    console.log('   - levels.years');
    console.log('   - years.subjects');
    console.log('   - subjects.lessons');
    console.log('\n💡 هذه الحقول مكررة لأن العلاقات موجودة عبر foreign keys');
    
    // في بيئة الإنتاج، يمكن إضافة تأكيد من المستخدم هنا
    
    // 3. تنفيذ التحديثات
    console.log('\n🔧 تنفيذ التحديثات...');
    
    // إزالة حقل years من جدول levels
    console.log('   - إزالة levels.years...');
    const { error: levelsError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE public.levels DROP COLUMN IF EXISTS years'
    });
    
    if (levelsError) {
      console.error('❌ خطأ في إزالة levels.years:', levelsError);
    } else {
      console.log('   ✅ تم إزالة levels.years بنجاح');
    }

    // إزالة حقل subjects من جدول years
    console.log('   - إزالة years.subjects...');
    const { error: yearsError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE public.years DROP COLUMN IF EXISTS subjects'
    });
    
    if (yearsError) {
      console.error('❌ خطأ في إزالة years.subjects:', yearsError);
    } else {
      console.log('   ✅ تم إزالة years.subjects بنجاح');
    }

    // إزالة حقل lessons من جدول subjects
    console.log('   - إزالة subjects.lessons...');
    const { error: subjectsError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE public.subjects DROP COLUMN IF EXISTS lessons'
    });
    
    if (subjectsError) {
      console.error('❌ خطأ في إزالة subjects.lessons:', subjectsError);
    } else {
      console.log('   ✅ تم إزالة subjects.lessons بنجاح');
    }

    // 4. عرض هيكل الجداول بعد التحديث
    console.log('\n📊 هيكل الجداول بعد التحديث:');
    await showTableStructure();

    console.log('\n🎉 تم إنجاز عملية إزالة المصفوفات المكررة بنجاح!');
    console.log('💡 التطبيق الآن يستخدم العلاقات المباشرة عبر foreign keys فقط');

  } catch (error) {
    console.error('❌ خطأ غير متوقع:', error);
    process.exit(1);
  }
}

async function showTableStructure() {
  try {
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT 
          table_name,
          column_name,
          data_type,
          is_nullable
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
          AND table_name IN ('levels', 'years', 'subjects', 'lessons')
        ORDER BY table_name, ordinal_position
      `
    });

    if (error) {
      console.error('خطأ في جلب هيكل الجداول:', error);
      return;
    }

    // تجميع البيانات حسب الجدول
    const tables = {};
    data?.forEach(row => {
      if (!tables[row.table_name]) {
        tables[row.table_name] = [];
      }
      tables[row.table_name].push(row);
    });

    // عرض هيكل كل جدول
    Object.keys(tables).forEach(tableName => {
      console.log(`\n   📋 ${tableName}:`);
      tables[tableName].forEach(column => {
        console.log(`      - ${column.column_name}: ${column.data_type} ${column.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`);
      });
    });

  } catch (error) {
    console.error('خطأ في عرض هيكل الجداول:', error);
  }
}

// تشغيل السكريبت
removeRedundantArrays();
