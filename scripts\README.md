# سكريبتات تحديث قاعدة البيانات

هذا المجلد يحتوي على السكريبتات المطلوبة لتحديث بنية المرحلة الثانوية في قاعدة البيانات Supabase.

## الملفات المتوفرة

### 1. `update-high-school-structure.sql`
سكريبت SQL مباشر يمكن تشغيله في Supabase SQL Editor.

**المحتوى:**
- حذف المستوى الثانوي القديم (`high`) والسنوات المرتبطة به
- إضافة المستويات الجديدة: جذع مشترك، الأولى باك، الثانية باك
- إضافة السنوات الجديدة مع التخصصات
- إضافة المواد الدراسية النموذجية
- تحديث العلاقات بين الجداول

### 2. `update-supabase.mjs`
سكريبت Node.js مبسط لتحديث قاعدة البيانات.

**المميزات:**
- سهل التشغيل
- يعرض تقدم العملية
- يتعامل مع الأخطاء بشكل جيد
- يضيف البيانات الأساسية فقط

### 3. `update-database.js`
سكريبت Node.js شامل مع جميع البيانات.

**المميزات:**
- يضيف جميع المواد الدراسية للتخصصات المختلفة
- يحدث جميع العلاقات
- يعرض تقرير مفصل عن النتائج

## طرق التشغيل

### الطريقة الأولى: SQL مباشر
1. افتح Supabase Dashboard
2. اذهب إلى SQL Editor
3. انسخ محتوى `update-high-school-structure.sql`
4. شغل السكريبت

### الطريقة الثانية: Node.js (مُوصى بها)
```bash
# انتقل إلى مجلد السكريبتات
cd scripts

# تثبيت التبعيات
npm install

# تشغيل السكريبت المبسط
npm run update-db

# أو تشغيل السكريبت الشامل
npm run update-full
```

### الطريقة الثالثة: تشغيل مباشر
```bash
# تأكد من وجود Node.js 18+ مثبت
node scripts/update-supabase.mjs
```

## البنية الجديدة

### المستويات الجديدة:
1. **جذع مشترك** (`trunk_common`)
   - جذع مشترك (`trunk_common_year`)

2. **الأولى باك** (`first_bac`)
   - الأولى باك علوم (`first_bac_sciences`)
   - الأولى باك آداب (`first_bac_literature`)
   - الأولى باك اقتصاد (`first_bac_economics`)

3. **الثانية باك** (`second_bac`)
   - الثانية باك علوم (`second_bac_sciences`)
   - الثانية باك آداب (`second_bac_literature`)
   - الثانية باك اقتصاد (`second_bac_economics`)

### المواد الدراسية:

#### جذع مشترك:
- الرياضيات 📐
- الفيزياء ⚛️
- الكيمياء 🧪
- علوم الحياة والأرض 🌱
- اللغة العربية 📚
- اللغة الفرنسية 🇫🇷
- اللغة الإنجليزية 🇺🇸
- التاريخ والجغرافيا 🌍
- الفلسفة 🤔
- التربية الإسلامية ☪️

#### الأولى باك علوم:
- الرياضيات، الفيزياء، الكيمياء، علوم الحياة والأرض
- اللغة العربية، الفرنسية، الإنجليزية
- الفلسفة، التربية الإسلامية

#### الأولى باك آداب:
- اللغة العربية، الفرنسية، الإنجليزية
- التاريخ والجغرافيا، الفلسفة
- الرياضيات، التربية الإسلامية

#### الأولى باك اقتصاد:
- الاقتصاد والتدبير 💼، المحاسبة 📊
- الرياضيات، اللغات
- التاريخ والجغرافيا، الفلسفة، التربية الإسلامية

## ملاحظات مهمة

⚠️ **تحذير:** هذه السكريبتات ستحذف جميع البيانات المرتبطة بالمستوى الثانوي القديم (`high`). تأكد من عمل نسخة احتياطية قبل التشغيل.

✅ **التحقق:** بعد تشغيل السكريبت، تحقق من:
- وجود المستويات الجديدة في جدول `levels`
- وجود السنوات الجديدة في جدول `years`
- وجود المواد الدراسية في جدول `subjects`
- صحة العلاقات بين الجداول

🔄 **إعادة التشغيل:** يمكن إعادة تشغيل السكريبتات بأمان، حيث أنها تحذف البيانات القديمة أولاً.

## الدعم

في حالة مواجهة أي مشاكل:
1. تحقق من صحة مفاتيح Supabase
2. تأكد من صلاحيات قاعدة البيانات
3. راجع رسائل الخطأ في وحدة التحكم
4. تحقق من حالة الاتصال بالإنترنت
