#!/usr/bin/env node

/**
 * سكريبت تحديث قاعدة البيانات Supabase
 * تشغيل: node scripts/update-supabase.mjs
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://ckjjqlbzflnxolflixkq.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNrampxbGJ6ZmxueG9sZmxpeGtxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQzNjcyOTQsImV4cCI6MjA0OTk0MzI5NH0.Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7E";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function updateHighSchoolStructure() {
  console.log("🚀 بدء تحديث بنية المرحلة الثانوية...");

  try {
    // 1. حذف البيانات القديمة
    console.log("🗑️ حذف البيانات القديمة...");
    
    // حذف السنوات القديمة للمرحلة الثانوية
    await supabase.from('years').delete().eq('level_id', 'high');
    console.log("✅ تم حذف السنوات القديمة");

    // حذف المستوى الثانوي القديم
    await supabase.from('levels').delete().eq('id', 'high');
    console.log("✅ تم حذف المستوى القديم");

    // 2. إضافة المستويات الجديدة
    console.log("📚 إضافة المستويات الجديدة...");
    
    const newLevels = [
      {
        id: "trunk_common",
        name: "جذع مشترك",
        description: "السنة الأولى من التعليم الثانوي - جذع مشترك",
        years: ["trunk_common_year"]
      },
      {
        id: "first_bac",
        name: "الأولى باك",
        description: "السنة الثانية من التعليم الثانوي مع التخصصات",
        years: ["first_bac_sciences", "first_bac_literature", "first_bac_economics"]
      },
      {
        id: "second_bac",
        name: "الثانية باك",
        description: "السنة الثالثة من التعليم الثانوي مع التخصصات",
        years: ["second_bac_sciences", "second_bac_literature", "second_bac_economics"]
      }
    ];

    const { error: levelsError } = await supabase.from('levels').insert(newLevels);
    if (levelsError) throw levelsError;
    console.log("✅ تم إضافة المستويات الجديدة");

    // 3. إضافة السنوات الجديدة
    console.log("📅 إضافة السنوات الجديدة...");
    
    const newYears = [
      { id: "trunk_common_year", name: "جذع مشترك", level_id: "trunk_common", subjects: [] },
      { id: "first_bac_sciences", name: "الأولى باك علوم", level_id: "first_bac", subjects: [] },
      { id: "first_bac_literature", name: "الأولى باك آداب", level_id: "first_bac", subjects: [] },
      { id: "first_bac_economics", name: "الأولى باك اقتصاد", level_id: "first_bac", subjects: [] },
      { id: "second_bac_sciences", name: "الثانية باك علوم", level_id: "second_bac", subjects: [] },
      { id: "second_bac_literature", name: "الثانية باك آداب", level_id: "second_bac", subjects: [] },
      { id: "second_bac_economics", name: "الثانية باك اقتصاد", level_id: "second_bac", subjects: [] }
    ];

    const { error: yearsError } = await supabase.from('years').insert(newYears);
    if (yearsError) throw yearsError;
    console.log("✅ تم إضافة السنوات الجديدة");

    // 4. إضافة المواد الدراسية لجذع مشترك
    console.log("📖 إضافة مواد جذع مشترك...");
    
    const trunkSubjects = [
      { id: "trunk_math", name: "الرياضيات", icon: "📐", year_id: "trunk_common_year", lessons: [] },
      { id: "trunk_physics", name: "الفيزياء", icon: "⚛️", year_id: "trunk_common_year", lessons: [] },
      { id: "trunk_chemistry", name: "الكيمياء", icon: "🧪", year_id: "trunk_common_year", lessons: [] },
      { id: "trunk_biology", name: "علوم الحياة والأرض", icon: "🌱", year_id: "trunk_common_year", lessons: [] },
      { id: "trunk_arabic", name: "اللغة العربية", icon: "📚", year_id: "trunk_common_year", lessons: [] },
      { id: "trunk_french", name: "اللغة الفرنسية", icon: "🇫🇷", year_id: "trunk_common_year", lessons: [] },
      { id: "trunk_english", name: "اللغة الإنجليزية", icon: "🇺🇸", year_id: "trunk_common_year", lessons: [] },
      { id: "trunk_history", name: "التاريخ والجغرافيا", icon: "🌍", year_id: "trunk_common_year", lessons: [] },
      { id: "trunk_philosophy", name: "الفلسفة", icon: "🤔", year_id: "trunk_common_year", lessons: [] },
      { id: "trunk_islamic", name: "التربية الإسلامية", icon: "☪️", year_id: "trunk_common_year", lessons: [] }
    ];

    const { error: trunkSubjectsError } = await supabase.from('subjects').insert(trunkSubjects);
    if (trunkSubjectsError) throw trunkSubjectsError;
    console.log("✅ تم إضافة مواد جذع مشترك");

    // 5. إضافة مواد الأولى باك علوم
    console.log("🔬 إضافة مواد الأولى باك علوم...");
    
    const firstBacSciSubjects = [
      { id: "first_bac_sci_math", name: "الرياضيات", icon: "📐", year_id: "first_bac_sciences", lessons: [] },
      { id: "first_bac_sci_physics", name: "الفيزياء", icon: "⚛️", year_id: "first_bac_sciences", lessons: [] },
      { id: "first_bac_sci_chemistry", name: "الكيمياء", icon: "🧪", year_id: "first_bac_sciences", lessons: [] },
      { id: "first_bac_sci_biology", name: "علوم الحياة والأرض", icon: "🌱", year_id: "first_bac_sciences", lessons: [] },
      { id: "first_bac_sci_arabic", name: "اللغة العربية", icon: "📚", year_id: "first_bac_sciences", lessons: [] },
      { id: "first_bac_sci_french", name: "اللغة الفرنسية", icon: "🇫🇷", year_id: "first_bac_sciences", lessons: [] },
      { id: "first_bac_sci_english", name: "اللغة الإنجليزية", icon: "🇺🇸", year_id: "first_bac_sciences", lessons: [] },
      { id: "first_bac_sci_philosophy", name: "الفلسفة", icon: "🤔", year_id: "first_bac_sciences", lessons: [] },
      { id: "first_bac_sci_islamic", name: "التربية الإسلامية", icon: "☪️", year_id: "first_bac_sciences", lessons: [] }
    ];

    const { error: firstBacSciError } = await supabase.from('subjects').insert(firstBacSciSubjects);
    if (firstBacSciError) throw firstBacSciError;
    console.log("✅ تم إضافة مواد الأولى باك علوم");

    // 6. تحديث مصفوفات المواد في السنوات
    console.log("🔄 تحديث مصفوفات المواد...");
    
    // تحديث جذع مشترك
    await supabase
      .from('years')
      .update({ 
        subjects: ["trunk_math", "trunk_physics", "trunk_chemistry", "trunk_biology", "trunk_arabic", "trunk_french", "trunk_english", "trunk_history", "trunk_philosophy", "trunk_islamic"]
      })
      .eq('id', 'trunk_common_year');

    // تحديث الأولى باك علوم
    await supabase
      .from('years')
      .update({ 
        subjects: ["first_bac_sci_math", "first_bac_sci_physics", "first_bac_sci_chemistry", "first_bac_sci_biology", "first_bac_sci_arabic", "first_bac_sci_french", "first_bac_sci_english", "first_bac_sci_philosophy", "first_bac_sci_islamic"]
      })
      .eq('id', 'first_bac_sciences');

    console.log("✅ تم تحديث مصفوفات المواد");

    // 7. عرض النتائج
    console.log("\n📊 النتائج النهائية:");
    
    const { data: levels } = await supabase
      .from('levels')
      .select('*')
      .in('id', ['trunk_common', 'first_bac', 'second_bac']);
    
    console.log("المستويات الجديدة:", levels?.length || 0);

    const { data: years } = await supabase
      .from('years')
      .select('*')
      .in('level_id', ['trunk_common', 'first_bac', 'second_bac']);
    
    console.log("السنوات الجديدة:", years?.length || 0);

    const { data: subjects } = await supabase
      .from('subjects')
      .select('*')
      .like('year_id', 'trunk_common_year')
      .or('year_id.like.first_bac_%');
    
    console.log("المواد الدراسية الجديدة:", subjects?.length || 0);

    console.log("\n🎉 تم تحديث قاعدة البيانات بنجاح!");
    console.log("\n📝 ملاحظة: يمكنك الآن إضافة المزيد من المواد للأولى باك آداب والاقتصاد والثانية باك حسب الحاجة.");

  } catch (error) {
    console.error("❌ خطأ في تحديث قاعدة البيانات:", error);
    process.exit(1);
  }
}

// تشغيل السكريبت
updateHighSchoolStructure();
