#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 بدء تحليل الأداء الشامل...\n');

// ألوان للطباعة
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n📋 ${description}...`, 'cyan');
  try {
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} مكتمل`, 'green');
    return output;
  } catch (error) {
    log(`❌ فشل في ${description}: ${error.message}`, 'red');
    return null;
  }
}

// 1. تنظيف وبناء المشروع
log('🧹 تنظيف المشروع...', 'yellow');
runCommand('rm -rf .next', 'حذف مجلد .next');
runCommand('rm -rf node_modules/.cache', 'حذف cache');

// 2. بناء المشروع
log('\n🔨 بناء المشروع...', 'yellow');
const buildOutput = runCommand('npm run build', 'بناء المشروع');

if (!buildOutput) {
  log('❌ فشل في بناء المشروع. توقف التحليل.', 'red');
  process.exit(1);
}

// 3. تحليل حجم Bundle
log('\n📦 تحليل حجم Bundle...', 'yellow');

// تحليل ملفات .next/static
const staticDir = path.join(process.cwd(), '.next/static');
if (fs.existsSync(staticDir)) {
  const analyzeBundle = () => {
    const chunks = [];
    const jsDir = path.join(staticDir, 'chunks');
    
    if (fs.existsSync(jsDir)) {
      const files = fs.readdirSync(jsDir);
      files.forEach(file => {
        if (file.endsWith('.js')) {
          const filePath = path.join(jsDir, file);
          const stats = fs.statSync(filePath);
          chunks.push({
            name: file,
            size: stats.size,
            sizeKB: (stats.size / 1024).toFixed(2)
          });
        }
      });
    }

    // ترتيب حسب الحجم
    chunks.sort((a, b) => b.size - a.size);

    log('\n📊 أكبر ملفات JavaScript:', 'magenta');
    chunks.slice(0, 10).forEach((chunk, index) => {
      const color = chunk.size > 500000 ? 'red' : chunk.size > 200000 ? 'yellow' : 'green';
      log(`${index + 1}. ${chunk.name}: ${chunk.sizeKB} KB`, color);
    });

    const totalSize = chunks.reduce((sum, chunk) => sum + chunk.size, 0);
    log(`\n📈 إجمالي حجم JavaScript: ${(totalSize / 1024 / 1024).toFixed(2)} MB`, 'bright');

    return { chunks, totalSize };
  };

  const bundleAnalysis = analyzeBundle();
  
  // تحذيرات للملفات الكبيرة
  const largeFiles = bundleAnalysis.chunks.filter(chunk => chunk.size > 500000);
  if (largeFiles.length > 0) {
    log('\n⚠️  ملفات كبيرة تحتاج تحسين:', 'yellow');
    largeFiles.forEach(file => {
      log(`   - ${file.name}: ${file.sizeKB} KB`, 'yellow');
    });
  }
}

// 4. تحليل الصور
log('\n🖼️  تحليل الصور...', 'yellow');
const publicDir = path.join(process.cwd(), 'public');
if (fs.existsSync(publicDir)) {
  const analyzeImages = (dir) => {
    const images = [];
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        images.push(...analyzeImages(filePath));
      } else if (/\.(jpg|jpeg|png|gif|webp|svg)$/i.test(file)) {
        images.push({
          name: path.relative(publicDir, filePath),
          size: stats.size,
          sizeKB: (stats.size / 1024).toFixed(2)
        });
      }
    });
    
    return images;
  };

  const images = analyzeImages(publicDir);
  images.sort((a, b) => b.size - a.size);

  if (images.length > 0) {
    log('\n📊 أكبر الصور:', 'magenta');
    images.slice(0, 5).forEach((image, index) => {
      const color = image.size > 1000000 ? 'red' : image.size > 500000 ? 'yellow' : 'green';
      log(`${index + 1}. ${image.name}: ${image.sizeKB} KB`, color);
    });

    const totalImageSize = images.reduce((sum, img) => sum + img.size, 0);
    log(`\n📈 إجمالي حجم الصور: ${(totalImageSize / 1024 / 1024).toFixed(2)} MB`, 'bright');

    // تحذيرات للصور الكبيرة
    const largeImages = images.filter(img => img.size > 1000000);
    if (largeImages.length > 0) {
      log('\n⚠️  صور كبيرة تحتاج تحسين:', 'yellow');
      largeImages.forEach(img => {
        log(`   - ${img.name}: ${img.sizeKB} KB`, 'yellow');
      });
    }
  } else {
    log('لا توجد صور في مجلد public', 'blue');
  }
}

// 5. تحليل dependencies
log('\n📚 تحليل Dependencies...', 'yellow');
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  log(`\n📊 إجمالي Dependencies: ${Object.keys(deps).length}`, 'magenta');
  
  // Dependencies كبيرة معروفة
  const heavyDeps = {
    'react': 'مكتبة React الأساسية',
    'next': 'إطار عمل Next.js',
    '@supabase/supabase-js': 'عميل Supabase',
    'pdfjs-dist': 'مكتبة PDF.js (ثقيلة)',
    'recharts': 'مكتبة الرسوم البيانية',
    'lucide-react': 'أيقونات Lucide'
  };

  log('\n📦 Dependencies المهمة:', 'blue');
  Object.keys(heavyDeps).forEach(dep => {
    if (deps[dep]) {
      log(`   ✅ ${dep}: ${deps[dep]} - ${heavyDeps[dep]}`, 'green');
    }
  });
}

// 6. فحص ملفات التكوين
log('\n⚙️  فحص ملفات التكوين...', 'yellow');

const configFiles = [
  'next.config.js',
  'tailwind.config.js',
  'tsconfig.json',
  'package.json'
];

configFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    log(`   ✅ ${file} موجود`, 'green');
  } else {
    log(`   ❌ ${file} مفقود`, 'red');
  }
});

// 7. تحليل أداء Next.js
log('\n⚡ تحليل إعدادات Next.js...', 'yellow');
const nextConfigPath = path.join(process.cwd(), 'next.config.js');
if (fs.existsSync(nextConfigPath)) {
  const nextConfig = fs.readFileSync(nextConfigPath, 'utf8');
  
  const optimizations = [
    { check: 'swcMinify: true', name: 'SWC Minification' },
    { check: 'compress: true', name: 'Response Compression' },
    { check: 'reactStrictMode: true', name: 'React Strict Mode' },
    { check: 'experimental:', name: 'Experimental Features' },
    { check: 'images:', name: 'Image Optimization' }
  ];

  log('\n🔧 تحسينات Next.js المفعلة:', 'blue');
  optimizations.forEach(opt => {
    if (nextConfig.includes(opt.check)) {
      log(`   ✅ ${opt.name}`, 'green');
    } else {
      log(`   ❌ ${opt.name} غير مفعل`, 'yellow');
    }
  });
}

// 8. إنشاء تقرير الأداء
log('\n📄 إنشاء تقرير الأداء...', 'yellow');

const report = {
  timestamp: new Date().toISOString(),
  analysis: {
    buildSuccess: !!buildOutput,
    bundleSize: 'تم التحليل',
    imageOptimization: 'تم الفحص',
    dependencies: 'تم التحليل',
    nextjsConfig: 'تم الفحص'
  },
  recommendations: [
    'استخدم AdvancedImage للصور الكبيرة',
    'فعل lazy loading للمكونات الثقيلة',
    'استخدم dynamic imports للمكتبات الكبيرة',
    'راقب Bundle Size بانتظام',
    'اختبر الأداء على أجهزة مختلفة'
  ]
};

const reportPath = path.join(process.cwd(), 'performance-report.json');
fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
log(`✅ تم حفظ التقرير في: ${reportPath}`, 'green');

// 9. نصائح التحسين
log('\n💡 نصائح للتحسين:', 'bright');
log('1. استخدم ComprehensivePerformanceOptimizer في الصفحات الرئيسية', 'cyan');
log('2. فعل AdvancedImage لجميع الصور', 'cyan');
log('3. استخدم التخزين المؤقت المتقدم للبيانات', 'cyan');
log('4. راقب Web Vitals بانتظام', 'cyan');
log('5. اختبر على شبكات بطيئة', 'cyan');

// 10. الخطوات التالية
log('\n🎯 الخطوات التالية:', 'bright');
log('1. شغل: npm run start للاختبار', 'magenta');
log('2. افتح Developer Tools وراقب Network tab', 'magenta');
log('3. استخدم Lighthouse لقياس الأداء', 'magenta');
log('4. راجع performance-report.json للتفاصيل', 'magenta');

log('\n🎉 انتهى تحليل الأداء بنجاح!', 'green');
log('📊 راجع النتائج أعلاه وطبق التوصيات لتحسين الأداء', 'bright');
