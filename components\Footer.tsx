'use client'

import Link from 'next/link';
import { GraduationCap, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { useState } from 'react';
import { clearAllCacheAndRefresh } from '@/backend/utils/dataLoader';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const [refreshing, setRefreshing] = useState(false);

  return (
    <footer className="w-full bg-sidebar border-t border-sidebar-border py-8" dir="rtl">
      <div className="max-w-[1280px] mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <Link href="/" className="flex items-center gap-2 mb-4">
              <GraduationCap className="h-6 w-6 text-primary" />
              <span className="font-bold text-xl text-primary">طلابي</span>
            </Link>
            <p className="text-sidebar-foreground mb-4 arabic-paragraph">
              منصة تعليمية متكاملة للطلاب في الوطن العربي، تقدم دروس وتمارين تفاعلية لمختلف المراحل الدراسية.
            </p>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4 text-primary arabic-heading">روابط مهمة</h3>
            <ul className="space-y-2">
              <li><Link href="/" className="text-sidebar-foreground hover:text-primary">الرئيسية</Link></li>
              <li><Link href="/levels" className="text-sidebar-foreground hover:text-primary">المستويات الدراسية</Link></li>
              <li><Link href="/about" className="text-sidebar-foreground hover:text-primary">عن المنصة</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4 text-primary arabic-heading">تواصل معنا</h3>
            <p className="text-sidebar-foreground mb-2 arabic-text">البريد الإلكتروني: <EMAIL></p>
          </div>
        </div>

        <div className="border-t border-sidebar-border mt-8 pt-6 text-center">
          <div className="flex flex-col items-center justify-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-1 text-xs"
              onClick={() => {
                try {
                  setRefreshing(true);

                  // مسح التخزين المؤقت من dataLoader
                  clearAllCacheAndRefresh();

                  // مسح localStorage
                  const keysToRemove: string[] = [];
                  for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (
                      key.includes('arab-edu') ||
                      key.includes('cache_') ||
                      key.includes('levels') ||
                      key.includes('years') ||
                      key.includes('subjects') ||
                      key.includes('lessons')
                    )) {
                      keysToRemove.push(key);
                    }
                  }

                  keysToRemove.forEach(key => localStorage.removeItem(key));

                  toast({
                    title: "تم تحديث البيانات",
                    description: `تم مسح ${keysToRemove.length} عنصر من التخزين المؤقت. سيتم إعادة تحميل الصفحة...`,
                    variant: "default",
                  });

                  // إعادة تحميل الصفحة بعد ثانيتين
                  setTimeout(() => {
                    window.location.reload();
                  }, 2000);

                } catch (error) {
                  console.error("خطأ في تحديث البيانات:", error);
                  toast({
                    title: "خطأ في تحديث البيانات",
                    description: "حدث خطأ أثناء تحديث البيانات، يرجى المحاولة مرة أخرى",
                    variant: "destructive",
                  });
                  setRefreshing(false);
                }
              }}
              disabled={refreshing}
            >
              <RefreshCw className={`h-3 w-3 ${refreshing ? 'animate-spin' : ''}`} />
              {refreshing ? 'جاري التحديث...' : 'تحديث البيانات'}
            </Button>

            <p className="text-sidebar-foreground">
              &copy; {currentYear} طلابي - جميع الحقوق محفوظة
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
