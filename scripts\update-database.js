/**
 * سكريبت تحديث قاعدة البيانات - تحديث بنية المرحلة الثانوية
 * يقوم بتحويل المرحلة الثانوية إلى ثلاثة أقسام منفصلة
 */

import { createClient } from '@supabase/supabase-js';

// إعدادات Supabase
const SUPABASE_URL = "https://ckjjqlbzflnxolflixkq.supabase.co";
const SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNrampxbGJ6ZmxueG9sZmxpeGtxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNDM2NzI5NCwiZXhwIjoyMDQ5OTQzMjk0fQ.Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7E"; // استخدم service role key

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// البيانات الجديدة
const newLevels = [
  {
    id: "trunk_common",
    name: "جذع مشترك",
    description: "السنة الأولى من التعليم الثانوي - جذع مشترك",
    years: ["trunk_common_year"]
  },
  {
    id: "first_bac",
    name: "الأولى باك",
    description: "السنة الثانية من التعليم الثانوي مع التخصصات",
    years: ["first_bac_sciences", "first_bac_literature", "first_bac_economics"]
  },
  {
    id: "second_bac",
    name: "الثانية باك",
    description: "السنة الثالثة من التعليم الثانوي مع التخصصات",
    years: ["second_bac_sciences", "second_bac_literature", "second_bac_economics"]
  }
];

const newYears = [
  // جذع مشترك
  {
    id: "trunk_common_year",
    name: "جذع مشترك",
    level_id: "trunk_common",
    subjects: []
  },
  // الأولى باك
  {
    id: "first_bac_sciences",
    name: "الأولى باك علوم",
    level_id: "first_bac",
    subjects: []
  },
  {
    id: "first_bac_literature",
    name: "الأولى باك آداب",
    level_id: "first_bac",
    subjects: []
  },
  {
    id: "first_bac_economics",
    name: "الأولى باك اقتصاد",
    level_id: "first_bac",
    subjects: []
  },
  // الثانية باك
  {
    id: "second_bac_sciences",
    name: "الثانية باك علوم",
    level_id: "second_bac",
    subjects: []
  },
  {
    id: "second_bac_literature",
    name: "الثانية باك آداب",
    level_id: "second_bac",
    subjects: []
  },
  {
    id: "second_bac_economics",
    name: "الثانية باك اقتصاد",
    level_id: "second_bac",
    subjects: []
  }
];

const newSubjects = [
  // مواد جذع مشترك
  { id: "trunk_math", name: "الرياضيات", icon: "📐", year_id: "trunk_common_year", lessons: [] },
  { id: "trunk_physics", name: "الفيزياء", icon: "⚛️", year_id: "trunk_common_year", lessons: [] },
  { id: "trunk_chemistry", name: "الكيمياء", icon: "🧪", year_id: "trunk_common_year", lessons: [] },
  { id: "trunk_biology", name: "علوم الحياة والأرض", icon: "🌱", year_id: "trunk_common_year", lessons: [] },
  { id: "trunk_arabic", name: "اللغة العربية", icon: "📚", year_id: "trunk_common_year", lessons: [] },
  { id: "trunk_french", name: "اللغة الفرنسية", icon: "🇫🇷", year_id: "trunk_common_year", lessons: [] },
  { id: "trunk_english", name: "اللغة الإنجليزية", icon: "🇺🇸", year_id: "trunk_common_year", lessons: [] },
  { id: "trunk_history", name: "التاريخ والجغرافيا", icon: "🌍", year_id: "trunk_common_year", lessons: [] },
  { id: "trunk_philosophy", name: "الفلسفة", icon: "🤔", year_id: "trunk_common_year", lessons: [] },
  { id: "trunk_islamic", name: "التربية الإسلامية", icon: "☪️", year_id: "trunk_common_year", lessons: [] },

  // مواد الأولى باك علوم
  { id: "first_bac_sci_math", name: "الرياضيات", icon: "📐", year_id: "first_bac_sciences", lessons: [] },
  { id: "first_bac_sci_physics", name: "الفيزياء", icon: "⚛️", year_id: "first_bac_sciences", lessons: [] },
  { id: "first_bac_sci_chemistry", name: "الكيمياء", icon: "🧪", year_id: "first_bac_sciences", lessons: [] },
  { id: "first_bac_sci_biology", name: "علوم الحياة والأرض", icon: "🌱", year_id: "first_bac_sciences", lessons: [] },
  { id: "first_bac_sci_arabic", name: "اللغة العربية", icon: "📚", year_id: "first_bac_sciences", lessons: [] },
  { id: "first_bac_sci_french", name: "اللغة الفرنسية", icon: "🇫🇷", year_id: "first_bac_sciences", lessons: [] },
  { id: "first_bac_sci_english", name: "اللغة الإنجليزية", icon: "🇺🇸", year_id: "first_bac_sciences", lessons: [] },
  { id: "first_bac_sci_philosophy", name: "الفلسفة", icon: "🤔", year_id: "first_bac_sciences", lessons: [] },
  { id: "first_bac_sci_islamic", name: "التربية الإسلامية", icon: "☪️", year_id: "first_bac_sciences", lessons: [] },

  // مواد الأولى باك آداب
  { id: "first_bac_lit_arabic", name: "اللغة العربية", icon: "📚", year_id: "first_bac_literature", lessons: [] },
  { id: "first_bac_lit_french", name: "اللغة الفرنسية", icon: "🇫🇷", year_id: "first_bac_literature", lessons: [] },
  { id: "first_bac_lit_english", name: "اللغة الإنجليزية", icon: "🇺🇸", year_id: "first_bac_literature", lessons: [] },
  { id: "first_bac_lit_history", name: "التاريخ والجغرافيا", icon: "🌍", year_id: "first_bac_literature", lessons: [] },
  { id: "first_bac_lit_philosophy", name: "الفلسفة", icon: "🤔", year_id: "first_bac_literature", lessons: [] },
  { id: "first_bac_lit_islamic", name: "التربية الإسلامية", icon: "☪️", year_id: "first_bac_literature", lessons: [] },
  { id: "first_bac_lit_math", name: "الرياضيات", icon: "📐", year_id: "first_bac_literature", lessons: [] },

  // مواد الأولى باك اقتصاد
  { id: "first_bac_eco_economics", name: "الاقتصاد والتدبير", icon: "💼", year_id: "first_bac_economics", lessons: [] },
  { id: "first_bac_eco_accounting", name: "المحاسبة", icon: "📊", year_id: "first_bac_economics", lessons: [] },
  { id: "first_bac_eco_math", name: "الرياضيات", icon: "📐", year_id: "first_bac_economics", lessons: [] },
  { id: "first_bac_eco_arabic", name: "اللغة العربية", icon: "📚", year_id: "first_bac_economics", lessons: [] },
  { id: "first_bac_eco_french", name: "اللغة الفرنسية", icon: "🇫🇷", year_id: "first_bac_economics", lessons: [] },
  { id: "first_bac_eco_english", name: "اللغة الإنجليزية", icon: "🇺🇸", year_id: "first_bac_economics", lessons: [] },
  { id: "first_bac_eco_history", name: "التاريخ والجغرافيا", icon: "🌍", year_id: "first_bac_economics", lessons: [] },
  { id: "first_bac_eco_philosophy", name: "الفلسفة", icon: "🤔", year_id: "first_bac_economics", lessons: [] },
  { id: "first_bac_eco_islamic", name: "التربية الإسلامية", icon: "☪️", year_id: "first_bac_economics", lessons: [] }
];

async function updateDatabase() {
  console.log("🚀 بدء تحديث قاعدة البيانات...");

  try {
    // 1. حذف البيانات القديمة
    console.log("🗑️ حذف البيانات القديمة...");
    
    // حذف السنوات القديمة
    const { error: deleteYearsError } = await supabase
      .from('years')
      .delete()
      .eq('level_id', 'high');
    
    if (deleteYearsError) {
      console.error("خطأ في حذف السنوات:", deleteYearsError);
      return;
    }

    // حذف المستوى القديم
    const { error: deleteLevelError } = await supabase
      .from('levels')
      .delete()
      .eq('id', 'high');
    
    if (deleteLevelError) {
      console.error("خطأ في حذف المستوى:", deleteLevelError);
      return;
    }

    console.log("✅ تم حذف البيانات القديمة بنجاح");

    // 2. إضافة المستويات الجديدة
    console.log("📚 إضافة المستويات الجديدة...");
    const { error: levelsError } = await supabase
      .from('levels')
      .insert(newLevels);
    
    if (levelsError) {
      console.error("خطأ في إضافة المستويات:", levelsError);
      return;
    }
    console.log("✅ تم إضافة المستويات الجديدة بنجاح");

    // 3. إضافة السنوات الجديدة
    console.log("📅 إضافة السنوات الجديدة...");
    const { error: yearsError } = await supabase
      .from('years')
      .insert(newYears);
    
    if (yearsError) {
      console.error("خطأ في إضافة السنوات:", yearsError);
      return;
    }
    console.log("✅ تم إضافة السنوات الجديدة بنجاح");

    // 4. إضافة المواد الدراسية الجديدة
    console.log("📖 إضافة المواد الدراسية الجديدة...");
    const { error: subjectsError } = await supabase
      .from('subjects')
      .insert(newSubjects);
    
    if (subjectsError) {
      console.error("خطأ في إضافة المواد:", subjectsError);
      return;
    }
    console.log("✅ تم إضافة المواد الدراسية الجديدة بنجاح");

    // 5. تحديث مصفوفات subjects في جدول years
    console.log("🔄 تحديث مصفوفات المواد...");
    
    const subjectUpdates = [
      {
        id: "trunk_common_year",
        subjects: ["trunk_math", "trunk_physics", "trunk_chemistry", "trunk_biology", "trunk_arabic", "trunk_french", "trunk_english", "trunk_history", "trunk_philosophy", "trunk_islamic"]
      },
      {
        id: "first_bac_sciences",
        subjects: ["first_bac_sci_math", "first_bac_sci_physics", "first_bac_sci_chemistry", "first_bac_sci_biology", "first_bac_sci_arabic", "first_bac_sci_french", "first_bac_sci_english", "first_bac_sci_philosophy", "first_bac_sci_islamic"]
      },
      {
        id: "first_bac_literature",
        subjects: ["first_bac_lit_arabic", "first_bac_lit_french", "first_bac_lit_english", "first_bac_lit_history", "first_bac_lit_philosophy", "first_bac_lit_islamic", "first_bac_lit_math"]
      },
      {
        id: "first_bac_economics",
        subjects: ["first_bac_eco_economics", "first_bac_eco_accounting", "first_bac_eco_math", "first_bac_eco_arabic", "first_bac_eco_french", "first_bac_eco_english", "first_bac_eco_history", "first_bac_eco_philosophy", "first_bac_eco_islamic"]
      }
    ];

    for (const update of subjectUpdates) {
      const { error } = await supabase
        .from('years')
        .update({ subjects: update.subjects })
        .eq('id', update.id);
      
      if (error) {
        console.error(`خطأ في تحديث مواد ${update.id}:`, error);
        return;
      }
    }

    console.log("✅ تم تحديث مصفوفات المواد بنجاح");

    // 6. عرض النتائج
    console.log("\n📊 النتائج النهائية:");
    
    const { data: levels } = await supabase
      .from('levels')
      .select('*')
      .in('id', ['trunk_common', 'first_bac', 'second_bac']);
    
    console.log("المستويات الجديدة:", levels);

    const { data: years } = await supabase
      .from('years')
      .select('*')
      .in('level_id', ['trunk_common', 'first_bac', 'second_bac']);
    
    console.log("السنوات الجديدة:", years);

    const { data: subjects } = await supabase
      .from('subjects')
      .select('*')
      .in('year_id', ['trunk_common_year', 'first_bac_sciences', 'first_bac_literature', 'first_bac_economics']);
    
    console.log("المواد الدراسية الجديدة:", subjects);

    console.log("\n🎉 تم تحديث قاعدة البيانات بنجاح!");

  } catch (error) {
    console.error("❌ خطأ عام في تحديث قاعدة البيانات:", error);
  }
}

// تشغيل السكريبت
updateDatabase();
