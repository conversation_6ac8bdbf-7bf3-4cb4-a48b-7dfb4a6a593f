import type { Metadata } from 'next'
import { <PERSON><PERSON><PERSON> } from 'next/font/google'
import './globals.css'
import { Providers } from './providers'
import CacheManager from '@/components/CacheManager'
import { PerformanceOptimizer, CriticalCSS, ResourceHints } from '@/components/PerformanceOptimizer'
import { GoogleAnalytics } from '@/components/Analytics'
// import PerformanceMonitor from '@/components/PerformanceMonitor'

const tajawal = Tajawal({
  subsets: ['arabic', 'latin'],
  weight: ['200', '300', '400', '500', '700', '800', '900'],
  variable: '--font-tajawal',
  display: 'swap',
  fallback: ['Arial', 'Helvetica', 'sans-serif'],
})

export const metadata: Metadata = {
  title: {
    default: 'منصة التعليم المغربي - تمارين ودروس تفاعلية',
    template: '%s | منصة التعليم المغربي'
  },
  description: 'منصة تعليمية متكاملة للطلاب في المغرب - دروس وتمارين تفاعلية لجميع المراحل الدراسية من الابتدائي إلى الباكالوريا وفقاً للمناهج المغربية',
  keywords: [
    'تعليم مغربي',
    'دروس تفاعلية',
    'تمارين تعليمية',
    'مناهج مغربية',
    'طلاب مغاربة',
    'تعليم ابتدائي المغرب',
    'تعليم إعدادي المغرب',
    'تعليم ثانوي المغرب',
    'باكالوريا مغربية',
    'امتحانات مغربية',
    'ملخصات دروس',
    'فروض منزلية',
    'تعليم إلكتروني المغرب',
    'منصة تعليمية مغربية',
    'مراجعة دروس',
    'جذع مشترك',
    'أولى باك',
    'ثانية باك'
  ],
  authors: [{ name: 'منصة التعليم المغربي', url: process.env.NEXT_PUBLIC_SITE_URL || 'https://www.tolabi.net' }],
  creator: 'منصة التعليم المغربي',
  publisher: 'منصة التعليم المغربي',
  category: 'Education',
  classification: 'Educational Platform',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://www.tolabi.net'),
  alternates: {
    canonical: '/',
    languages: {
      'ar': '/',
      'ar-MA': '/',
    }
  },
  openGraph: {
    title: 'منصة التعليم المغربي - تمارين ودروس تفاعلية',
    description: 'منصة تعليمية متكاملة للطلاب في المغرب - دروس وتمارين تفاعلية لجميع المراحل الدراسية وفقاً للمناهج المغربية',
    url: 'https://www.tolabi.net',
    siteName: 'منصة التعليم المغربي',
    locale: 'ar_MA',
    type: 'website',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'منصة التعليم العربي - تمارين ودروس تفاعلية',
        type: 'image/png',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'منصة التعليم المغربي - تمارين ودروس تفاعلية',
    description: 'منصة تعليمية متكاملة للطلاب في المغرب - دروس وتمارين تفاعلية لجميع المراحل الدراسية وفقاً للمناهج المغربية',
    images: ['/og-image.png'],
    creator: '@MoroccanEducation',
    site: '@MoroccanEducation',
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
  },
  other: {
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'منصة التعليم المغربي',
    'mobile-web-app-capable': 'yes',
    'msapplication-TileColor': '#2563eb',
    'theme-color': '#2563eb',
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" className="light-mode">
      <head>
        {/* DNS Prefetch for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://ckjjqlbzflnxolflixkq.supabase.co" />
        <link rel="dns-prefetch" href="https://www.google-analytics.com" />

        {/* Font optimization */}
        {/* eslint-disable-next-line @next/next/no-page-custom-font */}
        <link
          rel="preload"
          href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap"
          as="style"
        />
        {/* eslint-disable-next-line @next/next/no-page-custom-font */}
        <link
          rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap"
        />

        {/* Viewport and mobile optimization */}
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="format-detection" content="telephone=no" />

        {/* PWA and mobile app meta tags */}
        <meta name="application-name" content="منصة التعليم المغربي" />
        <meta name="apple-mobile-web-app-title" content="منصة التعليم المغربي" />
        <meta name="msapplication-tooltip" content="منصة التعليم المغربي - تمارين ودروس تفاعلية" />

        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {/* Google Analytics */}
        {process.env.NEXT_PUBLIC_GA_ID && (
          <>
            <script
              async
              src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
            />
            <script
              dangerouslySetInnerHTML={{
                __html: `
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}', {
                    page_title: document.title,
                    page_location: window.location.href,
                    send_page_view: true
                  });
                `,
              }}
            />
          </>
        )}

        {/* Structured Data for Organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "EducationalOrganization",
              "name": "منصة التعليم المغربي",
              "alternateName": "Moroccan Education Platform",
              "url": process.env.NEXT_PUBLIC_SITE_URL || "https://www.tolabi.net",
              "logo": `${process.env.NEXT_PUBLIC_SITE_URL || "https://www.tolabi.net"}/logo.png`,
              "description": "منصة تعليمية متكاملة للطلاب في المغرب - دروس وتمارين تفاعلية لجميع المراحل الدراسية وفقاً للمناهج المغربية",
              "educationalCredentialAwarded": "شهادة إتمام",
              "hasCredential": "شهادة معتمدة",
              "areaServed": {
                "@type": "Country",
                "name": "المغرب",
                "alternateName": "Morocco"
              },
              "teaches": [
                "الرياضيات",
                "العلوم الفيزيائية",
                "علوم الحياة والأرض",
                "اللغة العربية",
                "اللغة الفرنسية",
                "التاريخ والجغرافيا",
                "التربية الإسلامية",
                "الفلسفة"
              ],
              "audience": {
                "@type": "EducationalAudience",
                "educationalRole": "student",
                "audienceType": "طلاب المراحل الدراسية في المغرب",
                "geographicArea": {
                  "@type": "Country",
                  "name": "المغرب"
                }
              },
              "provider": {
                "@type": "Organization",
                "name": "منصة التعليم المغربي"
              },
              "inLanguage": ["ar", "fr"],
              "availableLanguage": ["ar", "fr"]
            })
          }}
        />
      </head>
      <body className={`${tajawal.variable} font-tajawal antialiased bg-background text-foreground`}>
        <Providers>
          <CacheManager />
          <PerformanceOptimizer />
          <CriticalCSS />
          <ResourceHints />
          <GoogleAnalytics />
          {children}
          {/* <PerformanceMonitor /> */}
        </Providers>
      </body>
    </html>
  )
}
