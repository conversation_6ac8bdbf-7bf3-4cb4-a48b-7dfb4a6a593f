'use client'

import Link from "next/link";
import { GraduationCap } from "lucide-react";
import HeaderDropdown from "./HeaderDropdown";
import { Level, Year } from "@/data/types";

interface DesktopHeaderProps {
  primaryLevels: Level[];
  middleLevels: Level[];
  trunkCommonLevels: Level[];
  firstBacLevels: Level[];
  secondBacLevels: Level[];
  yearsByLevel: Record<string, Year[]>;
  loading: boolean;
}

const DesktopHeader = ({
  primaryLevels,
  middleLevels,
  trunkCommonLevels,
  firstBacLevels,
  secondBacLevels,
  yearsByLevel,
  loading,
}: DesktopHeaderProps) => {
  return (
    <div className="w-full bg-primary text-white h-14" dir="rtl">
      <div className="max-w-[1280px] mx-auto px-4 flex items-center h-full">
        {/* Logo on the right (RTL: actually appears on the right) */}
        <div className="w-24 flex justify-start">
          <Link href="/" className="font-bold text-xl text-white flex items-center gap-2">
            <GraduationCap className="h-6 w-6 mr-2" />
            طلابي
          </Link>
        </div>

        {/* Navigation in the center with RTL-aware spacing */}
        <div className="flex-1 flex justify-center">
          <div className="rtl-navigation">
            <HeaderDropdown
              title="المرحلة الإبتدائية"
              levels={primaryLevels}
              yearsByLevel={yearsByLevel}
              loading={loading}
            />
            <HeaderDropdown
              title="المرحلة الإعدادية"
              levels={middleLevels}
              yearsByLevel={yearsByLevel}
              loading={loading}
            />
            <HeaderDropdown
              title="جذع مشترك"
              levels={trunkCommonLevels}
              yearsByLevel={yearsByLevel}
              loading={loading}
            />
            <HeaderDropdown
              title="الأولى باك"
              levels={firstBacLevels}
              yearsByLevel={yearsByLevel}
              loading={loading}
            />
            <HeaderDropdown
              title="الثانية باك"
              levels={secondBacLevels}
              yearsByLevel={yearsByLevel}
              loading={loading}
            />
          </div>
        </div>

        {/* Empty space on the left for balance */}
        <div className="w-24"></div>
      </div>
    </div>
  );
};

export default DesktopHeader;
