'use client'

import Link from "next/link";
import { Menu, ChevronDown, ChevronUp, GraduationCap } from "lucide-react";
import { Button } from "./ui/button";
import { Level, Year } from "@/data/types";
import { useState } from "react";

// Function to sort years by grade number
const sortYearsByGrade = (years: Year[]): Year[] => {
  return [...years].sort((a, b) => {
    // Map grade names to numeric values for sorting
    const getGradeValue = (name: string): number => {
      // Primary grades
      if (name.includes('الأول') && name.includes('ابتدائي')) return 1;
      if (name.includes('الثاني') && name.includes('ابتدائي')) return 2;
      if (name.includes('الثالث') && name.includes('ابتدائي')) return 3;
      if (name.includes('الرابع') && name.includes('ابتدائي')) return 4;
      if (name.includes('الخامس') && name.includes('ابتدائي')) return 5;
      if (name.includes('السادس') && name.includes('ابتدائي')) return 6;

      // Middle grades
      if (name.includes('الأول') && name.includes('إعدادي')) return 7;
      if (name.includes('الثاني') && name.includes('إعدادي')) return 8;
      if (name.includes('الثالث') && name.includes('إعدادي')) return 9;

      // High school - new structure
      if (name.includes('جذع مشترك')) return 10;
      if (name.includes('الأولى باك')) return 11;
      if (name.includes('الثانية باك')) return 12;

      // Fallback to extract numbers
      const match = name.match(/(\d+)/);
      return match ? parseInt(match[1]) : 999; // Default high value for unknown
    };

    return getGradeValue(a.name) - getGradeValue(b.name);
  });
};

interface MobileHeaderProps {
  primaryLevels: Level[];
  middleLevels: Level[];
  trunkCommonLevels: Level[];
  firstBacLevels: Level[];
  secondBacLevels: Level[];
  yearsByLevel: Record<string, Year[]>;
  loading: boolean;
  mobileMenuOpen: boolean;
  toggleMobileMenu: () => void;
}

const MobileHeader = ({
  primaryLevels,
  middleLevels,
  trunkCommonLevels,
  firstBacLevels,
  secondBacLevels,
  yearsByLevel,
  loading,
  mobileMenuOpen,
  toggleMobileMenu,
}: MobileHeaderProps) => {
  const [activeLevel, setActiveLevel] = useState<string | null>(null);

  const toggleLevel = (level: string) => {
    setActiveLevel(activeLevel === level ? null : level);
  };
  return (
    <div className="w-full bg-primary text-white relative" dir="rtl">
      <div className="h-14">
        <div className="max-w-[1280px] mx-auto px-4 flex items-center h-full relative">
          {/* Right side - Menu button (RTL: appears on the right) */}
          <div className="absolute left-4">
            <Button
              variant="ghost"
              size="icon"
              className="text-white"
              onClick={toggleMobileMenu}
            >
              <Menu className="h-6 w-6" />
            </Button>
          </div>

          {/* Center - Logo */}
          <div className="flex-1 flex justify-center items-center">
            <Link href="/" className="font-bold text-xl text-white flex items-center gap-2">
              <GraduationCap className="h-5 w-5" />
              <span>طلابي</span>
            </Link>
          </div>

          {/* Left side - Empty space for balance */}
          <div className="w-10"></div>
        </div>
      </div>

      {mobileMenuOpen && (
        <div className="w-full bg-background border-b border-primary/20 z-50 max-h-[calc(100vh-3.5rem)] overflow-y-auto">
          <div className="py-4 px-2 max-w-[1280px] mx-auto">
            {/* المرحلة الابتدائية */}
            <div className="border-b border-border pb-4 mb-4">
              <div
                className={`px-3 py-3 font-medium flex justify-between items-center cursor-pointer rounded-md mx-1 my-2 shadow-sm transition-all ${activeLevel === 'primary' ? 'bg-primary text-primary-foreground' : 'bg-card text-card-foreground'}`}
                onClick={() => toggleLevel('primary')}
              >
                <span className="font-bold text-sm">المرحلة الإبتدائية</span>
                {activeLevel === 'primary' ?
                  <ChevronUp className="h-4 w-4 flex-shrink-0" /> :
                  <ChevronDown className="h-4 w-4 flex-shrink-0" />
                }
              </div>

              {activeLevel === 'primary' && !loading && (
                <div className="mt-1 space-y-1">
                  {primaryLevels.map((level) => (
                    <div key={level.id} className="px-1">
                      {sortYearsByGrade(yearsByLevel[level.id] || []).map((year) => (
                        <Link
                          key={year.id}
                          href={`/year/${year.id}`}
                          className="block px-3 py-2 bg-card hover:bg-accent rounded-md mx-1 my-1 text-sm transition-colors shadow-sm text-foreground truncate"
                          onClick={toggleMobileMenu}
                        >
                          {year.name}
                        </Link>
                      ))}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* المرحلة الإعدادية */}
            <div className="border-b border-border pb-4 mb-4">
              <div
                className={`px-3 py-3 font-medium flex justify-between items-center cursor-pointer rounded-md mx-1 my-2 shadow-sm transition-all ${activeLevel === 'middle' ? 'bg-primary text-primary-foreground' : 'bg-card text-card-foreground'}`}
                onClick={() => toggleLevel('middle')}
              >
                <span className="font-bold text-sm">المرحلة الإعدادية</span>
                {activeLevel === 'middle' ?
                  <ChevronUp className="h-4 w-4 flex-shrink-0" /> :
                  <ChevronDown className="h-4 w-4 flex-shrink-0" />
                }
              </div>

              {activeLevel === 'middle' && !loading && (
                <div className="mt-1 space-y-1">
                  {middleLevels.map((level) => (
                    <div key={level.id} className="px-1">
                      {sortYearsByGrade(yearsByLevel[level.id] || []).map((year) => (
                        <Link
                          key={year.id}
                          href={`/year/${year.id}`}
                          className="block px-3 py-2 bg-card hover:bg-accent rounded-md mx-1 my-1 text-sm transition-colors shadow-sm text-foreground truncate"
                          onClick={toggleMobileMenu}
                        >
                          {year.name}
                        </Link>
                      ))}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* جذع مشترك */}
            <div className="border-b border-border pb-4 mb-4">
              <div
                className={`px-3 py-3 font-medium flex justify-between items-center cursor-pointer rounded-md mx-1 my-2 shadow-sm transition-all ${activeLevel === 'trunk_common' ? 'bg-primary text-primary-foreground' : 'bg-card text-card-foreground'}`}
                onClick={() => toggleLevel('trunk_common')}
              >
                <span className="font-bold text-sm">جذع مشترك</span>
                {activeLevel === 'trunk_common' ?
                  <ChevronUp className="h-4 w-4 flex-shrink-0" /> :
                  <ChevronDown className="h-4 w-4 flex-shrink-0" />
                }
              </div>

              {activeLevel === 'trunk_common' && !loading && (
                <div className="mt-1 space-y-1">
                  {trunkCommonLevels.map((level) => (
                    <div key={level.id} className="px-1">
                      {sortYearsByGrade(yearsByLevel[level.id] || []).map((year) => (
                        <Link
                          key={year.id}
                          href={`/year/${year.id}`}
                          className="block px-3 py-2 bg-card hover:bg-accent rounded-md mx-1 my-1 text-sm transition-colors shadow-sm text-foreground truncate"
                          onClick={toggleMobileMenu}
                        >
                          {year.name}
                        </Link>
                      ))}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* الأولى باك */}
            <div className="border-b border-border pb-4 mb-4">
              <div
                className={`px-3 py-3 font-medium flex justify-between items-center cursor-pointer rounded-md mx-1 my-2 shadow-sm transition-all ${activeLevel === 'first_bac' ? 'bg-primary text-primary-foreground' : 'bg-card text-card-foreground'}`}
                onClick={() => toggleLevel('first_bac')}
              >
                <span className="font-bold text-sm">الأولى باك</span>
                {activeLevel === 'first_bac' ?
                  <ChevronUp className="h-4 w-4 flex-shrink-0" /> :
                  <ChevronDown className="h-4 w-4 flex-shrink-0" />
                }
              </div>

              {activeLevel === 'first_bac' && !loading && (
                <div className="mt-1 space-y-1">
                  {firstBacLevels.map((level) => (
                    <div key={level.id} className="px-1">
                      {sortYearsByGrade(yearsByLevel[level.id] || []).map((year) => (
                        <Link
                          key={year.id}
                          href={`/year/${year.id}`}
                          className="block px-3 py-2 bg-card hover:bg-accent rounded-md mx-1 my-1 text-sm transition-colors shadow-sm text-foreground truncate"
                          onClick={toggleMobileMenu}
                        >
                          {year.name}
                        </Link>
                      ))}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* الثانية باك */}
            <div className="border-b border-border pb-4 mb-4">
              <div
                className={`px-3 py-3 font-medium flex justify-between items-center cursor-pointer rounded-md mx-1 my-2 shadow-sm transition-all ${activeLevel === 'second_bac' ? 'bg-primary text-primary-foreground' : 'bg-card text-card-foreground'}`}
                onClick={() => toggleLevel('second_bac')}
              >
                <span className="font-bold text-sm">الثانية باك</span>
                {activeLevel === 'second_bac' ?
                  <ChevronUp className="h-4 w-4 flex-shrink-0" /> :
                  <ChevronDown className="h-4 w-4 flex-shrink-0" />
                }
              </div>

              {activeLevel === 'second_bac' && !loading && (
                <div className="mt-1 space-y-1">
                  {secondBacLevels.map((level) => (
                    <div key={level.id} className="px-1">
                      {sortYearsByGrade(yearsByLevel[level.id] || []).map((year) => (
                        <Link
                          key={year.id}
                          href={`/year/${year.id}`}
                          className="block px-3 py-2 bg-card hover:bg-accent rounded-md mx-1 my-1 text-sm transition-colors shadow-sm text-foreground truncate"
                          onClick={toggleMobileMenu}
                        >
                          {year.name}
                        </Link>
                      ))}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MobileHeader;



