'use client'

import { useMemo } from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useLevelsWithYears } from '@/hooks/use-education-data';

import type { Year } from '@/data/types';

// دالة مساعدة لترتيب السنوات الدراسية (محسنة مع useMemo)

// دالة مساعدة لترتيب السنوات الدراسية
const sortYears = (years: Year[]): Year[] => {
  return [...years].sort((a, b) => {
    // Map grade names to numeric values for sorting
    const getGradeValue = (name: string): number => {
      // Primary grades
      if (name.includes('الأول') && name.includes('ابتدائي')) return 1;
      if (name.includes('الثاني') && name.includes('ابتدائي')) return 2;
      if (name.includes('الثالث') && name.includes('ابتدائي')) return 3;
      if (name.includes('الرابع') && name.includes('ابتدائي')) return 4;
      if (name.includes('الخامس') && name.includes('ابتدائي')) return 5;
      if (name.includes('السادس') && name.includes('ابتدائي')) return 6;

      // Middle grades
      if (name.includes('الأول') && name.includes('إعدادي')) return 7;
      if (name.includes('الثاني') && name.includes('إعدادي')) return 8;
      if (name.includes('الثالث') && name.includes('إعدادي')) return 9;

      // High school - new structure
      if (name.includes('جذع مشترك')) return 10;
      if (name.includes('الأولى باك')) return 11;
      if (name.includes('الثانية باك')) return 12;

      // Fallback to extract numbers
      const match = name.match(/(\d+)/);
      return match ? parseInt(match[1]) : 999; // Default high value for unknown
    };

    return getGradeValue(a.name) - getGradeValue(b.name);
  });
};

export default function LevelsClient() {
  const { data: levelsWithYears, isLoading, isError } = useLevelsWithYears();

  // ترتيب السنوات لكل مستوى باستخدام useMemo للأداء الأفضل
  const sortedLevelsWithYears = useMemo(() => {
    return levelsWithYears.map(level => ({
      ...level,
      years: sortYears(level.years)
    }));
  }, [levelsWithYears]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="container mx-auto px-4 py-12 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-lg">جاري تحميل البيانات...</p>
          </div>
        </div>
        <div className="mt-auto">
          <Footer />
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="container mx-auto px-4 py-12 flex items-center justify-center">
          <div className="text-center">
            <p className="text-lg text-destructive">حدث خطأ أثناء تحميل البيانات</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            >
              إعادة المحاولة
            </button>
          </div>
        </div>
        <div className="mt-auto">
          <Footer />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-primary mb-4">مرحباً بك في طلابي</h1>
          <p className="text-muted-foreground mx-auto max-w-2xl">
            منصتك التعليمية التفاعلية المغربية. اختر مرحلتك الدراسية وابدأ رحلة التعلم وفق المنهاج المغربي!
          </p>
        </div>

        <div className="space-y-12">
          {sortedLevelsWithYears.map((level) => (
            <div key={level.id} className="mb-16">
              <h2 className="level-heading">{level.name}</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {level.years.map((year) => (
                  <Link
                    href={`/year/${year.id}`}
                    key={year.id}
                    className="level-card transition-all duration-300 hover:shadow-md hover:scale-[1.02]"
                    dir="rtl"
                  >
                    <span className="text-xl font-bold">{year.name}</span>
                    <div className="bg-muted rounded-full p-2 transition-all duration-300 hover:bg-primary/20">
                      <ArrowLeft className="h-5 w-5 text-primary transition-transform hover:translate-x-1" />
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-auto">
        <Footer />
      </div>
    </div>
  );
}
