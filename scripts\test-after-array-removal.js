#!/usr/bin/env node

/**
 * سكريبت اختبار التطبيق بعد إزالة المصفوفات المكررة
 * Test Script After Redundant Arrays Removal
 * 
 * هذا السكريبت يختبر جميع الوظائف للتأكد من أن التطبيق يعمل بشكل صحيح
 * بعد إزالة المصفوفات المكررة من قاعدة البيانات
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// تحميل متغيرات البيئة
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ خطأ: متغيرات البيئة مفقودة');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testApplicationAfterArrayRemoval() {
  console.log('🧪 بدء اختبار التطبيق بعد إزالة المصفوفات المكررة...\n');

  let allTestsPassed = true;

  try {
    // 1. اختبار جلب المستويات
    console.log('📚 اختبار جلب المستويات...');
    const { data: levels, error: levelsError } = await supabase
      .from('levels')
      .select('*')
      .order('display_order', { ascending: true });

    if (levelsError) {
      console.error('❌ خطأ في جلب المستويات:', levelsError);
      allTestsPassed = false;
    } else {
      console.log(`✅ تم جلب ${levels?.length || 0} مستوى بنجاح`);
      
      // التأكد من عدم وجود حقل years
      if (levels && levels.length > 0) {
        const hasYearsField = levels[0].hasOwnProperty('years');
        if (hasYearsField) {
          console.error('❌ حقل years ما زال موجود في جدول levels');
          allTestsPassed = false;
        } else {
          console.log('✅ تم إزالة حقل years من جدول levels بنجاح');
        }
      }
    }

    // 2. اختبار جلب السنوات
    console.log('\n📅 اختبار جلب السنوات...');
    const { data: years, error: yearsError } = await supabase
      .from('years')
      .select('*');

    if (yearsError) {
      console.error('❌ خطأ في جلب السنوات:', yearsError);
      allTestsPassed = false;
    } else {
      console.log(`✅ تم جلب ${years?.length || 0} سنة بنجاح`);
      
      // التأكد من عدم وجود حقل subjects
      if (years && years.length > 0) {
        const hasSubjectsField = years[0].hasOwnProperty('subjects');
        if (hasSubjectsField) {
          console.error('❌ حقل subjects ما زال موجود في جدول years');
          allTestsPassed = false;
        } else {
          console.log('✅ تم إزالة حقل subjects من جدول years بنجاح');
        }
      }
    }

    // 3. اختبار جلب المواد
    console.log('\n📖 اختبار جلب المواد...');
    const { data: subjects, error: subjectsError } = await supabase
      .from('subjects')
      .select('*');

    if (subjectsError) {
      console.error('❌ خطأ في جلب المواد:', subjectsError);
      allTestsPassed = false;
    } else {
      console.log(`✅ تم جلب ${subjects?.length || 0} مادة بنجاح`);
      
      // التأكد من عدم وجود حقل lessons
      if (subjects && subjects.length > 0) {
        const hasLessonsField = subjects[0].hasOwnProperty('lessons');
        if (hasLessonsField) {
          console.error('❌ حقل lessons ما زال موجود في جدول subjects');
          allTestsPassed = false;
        } else {
          console.log('✅ تم إزالة حقل lessons من جدول subjects بنجاح');
        }
      }
    }

    // 4. اختبار جلب الدروس
    console.log('\n📝 اختبار جلب الدروس...');
    const { data: lessons, error: lessonsError } = await supabase
      .from('lessons')
      .select('*');

    if (lessonsError) {
      console.error('❌ خطأ في جلب الدروس:', lessonsError);
      allTestsPassed = false;
    } else {
      console.log(`✅ تم جلب ${lessons?.length || 0} درس بنجاح`);
    }

    // 5. اختبار العلاقات - السنوات حسب المستوى
    if (levels && levels.length > 0) {
      console.log('\n🔗 اختبار العلاقات - السنوات حسب المستوى...');
      const firstLevel = levels[0];
      
      const { data: yearsByLevel, error: yearsByLevelError } = await supabase
        .from('years')
        .select('*')
        .eq('level_id', firstLevel.id);

      if (yearsByLevelError) {
        console.error('❌ خطأ في جلب السنوات حسب المستوى:', yearsByLevelError);
        allTestsPassed = false;
      } else {
        console.log(`✅ تم جلب ${yearsByLevel?.length || 0} سنة للمستوى "${firstLevel.name}"`);
      }
    }

    // 6. اختبار العلاقات - المواد حسب السنة
    if (years && years.length > 0) {
      console.log('\n🔗 اختبار العلاقات - المواد حسب السنة...');
      const firstYear = years[0];
      
      const { data: subjectsByYear, error: subjectsByYearError } = await supabase
        .from('subjects')
        .select('*')
        .eq('year_id', firstYear.id);

      if (subjectsByYearError) {
        console.error('❌ خطأ في جلب المواد حسب السنة:', subjectsByYearError);
        allTestsPassed = false;
      } else {
        console.log(`✅ تم جلب ${subjectsByYear?.length || 0} مادة للسنة "${firstYear.name}"`);
      }
    }

    // 7. اختبار العلاقات - الدروس حسب المادة
    if (subjects && subjects.length > 0) {
      console.log('\n🔗 اختبار العلاقات - الدروس حسب المادة...');
      const firstSubject = subjects[0];
      
      const { data: lessonsBySubject, error: lessonsBySubjectError } = await supabase
        .from('lessons')
        .select('*')
        .eq('subject_id', firstSubject.id);

      if (lessonsBySubjectError) {
        console.error('❌ خطأ في جلب الدروس حسب المادة:', lessonsBySubjectError);
        allTestsPassed = false;
      } else {
        console.log(`✅ تم جلب ${lessonsBySubject?.length || 0} درس للمادة "${firstSubject.name}"`);
      }
    }

    // النتيجة النهائية
    console.log('\n' + '='.repeat(50));
    if (allTestsPassed) {
      console.log('🎉 جميع الاختبارات نجحت! التطبيق يعمل بشكل صحيح بعد إزالة المصفوفات المكررة');
      console.log('✅ العلاقات تعمل بشكل صحيح عبر foreign keys');
      console.log('✅ تم إزالة جميع الحقول المكررة بنجاح');
    } else {
      console.log('❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه');
    }

  } catch (error) {
    console.error('❌ خطأ غير متوقع:', error);
    allTestsPassed = false;
  }

  return allTestsPassed;
}

// تشغيل الاختبار
testApplicationAfterArrayRemoval()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('خطأ في تشغيل الاختبار:', error);
    process.exit(1);
  });
