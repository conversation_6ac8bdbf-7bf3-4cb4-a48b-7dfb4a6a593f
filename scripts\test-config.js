#!/usr/bin/env node

/**
 * اختبار التكوين المركزي
 * Test Central Configuration
 * 
 * يتحقق هذا السكريبت من أن جميع الملفات تستخدم متغير البيئة بشكل صحيح
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 اختبار التكوين المركزي...\n');

// قائمة الملفات التي يجب أن تستخدم متغير البيئة
const filesToCheck = [
  {
    path: 'lib/config.ts',
    shouldContain: ['process.env.NEXT_PUBLIC_SITE_URL', 'siteConfig'],
    description: 'ملف التكوين المركزي'
  },
  {
    path: 'app/layout.tsx',
    shouldContain: ['process.env.NEXT_PUBLIC_SITE_URL'],
    description: 'ملف Layout الرئيسي'
  },
  {
    path: 'app/sitemap.ts',
    shouldContain: ['siteConfig'],
    description: 'خريطة الموقع'
  },
  {
    path: 'app/robots.ts',
    shouldContain: ['siteConfig'],
    description: 'ملف robots.txt الديناميكي'
  },
  {
    path: 'lib/seo.ts',
    shouldContain: ['siteConfig'],
    description: 'مكتبة SEO'
  },
  {
    path: 'components/Breadcrumb.tsx',
    shouldContain: ['siteConfig'],
    description: 'مكون مسارات التنقل'
  },
  {
    path: '.env.example',
    shouldContain: ['NEXT_PUBLIC_SITE_URL=https://www.tolabi.net'],
    description: 'ملف متغيرات البيئة المثال'
  }
];

let passedTests = 0;
let totalTests = 0;

function checkFile(fileInfo) {
  const { path: filePath, shouldContain, description } = fileInfo;
  const fullPath = path.join(path.dirname(__dirname), filePath);
  
  console.log(`📁 فحص ${description} (${filePath})...`);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`   ❌ الملف غير موجود`);
    return false;
  }
  
  const content = fs.readFileSync(fullPath, 'utf8');
  let allFound = true;
  
  shouldContain.forEach(searchText => {
    totalTests++;
    if (content.includes(searchText)) {
      console.log(`   ✅ يحتوي على: ${searchText}`);
      passedTests++;
    } else {
      console.log(`   ❌ لا يحتوي على: ${searchText}`);
      allFound = false;
    }
  });
  
  return allFound;
}

// فحص جميع الملفات
console.log('🔍 فحص الملفات...\n');

filesToCheck.forEach(fileInfo => {
  checkFile(fileInfo);
  console.log('');
});

// فحص إضافي: التأكد من عدم وجود روابط مباشرة
console.log('🔍 فحص الروابط المباشرة...\n');

const directUrlPattern = /https:\/\/www\.tolabi\.net/g;
const filesToCheckForDirectUrls = [
  'app/layout.tsx',
  'app/sitemap.ts',
  'lib/seo.ts',
  'components/Breadcrumb.tsx'
];

filesToCheckForDirectUrls.forEach(filePath => {
  const fullPath = path.join(path.dirname(__dirname), filePath);
  if (fs.existsSync(fullPath)) {
    const content = fs.readFileSync(fullPath, 'utf8');
    const matches = content.match(directUrlPattern);
    
    if (matches) {
      console.log(`⚠️  ${filePath}: وُجدت ${matches.length} روابط مباشرة`);
      matches.forEach(match => {
        const lineNumber = content.substring(0, content.indexOf(match)).split('\n').length;
        console.log(`   السطر ${lineNumber}: ${match}`);
      });
    } else {
      console.log(`✅ ${filePath}: لا توجد روابط مباشرة`);
    }
  }
});

// النتائج النهائية
console.log('\n' + '='.repeat(50));
console.log('📊 نتائج الاختبار');
console.log('='.repeat(50));
console.log(`✅ اختبارات نجحت: ${passedTests}`);
console.log(`❌ اختبارات فشلت: ${totalTests - passedTests}`);
console.log(`📈 معدل النجاح: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 ممتاز! جميع الاختبارات نجحت!');
  console.log('✅ التكوين المركزي يعمل بشكل صحيح');
  console.log('✅ يمكنك الآن تغيير الرابط من ملف .env فقط');
} else {
  console.log('\n⚠️  هناك بعض المشاكل التي تحتاج إلى إصلاح');
  console.log('📝 راجع الأخطاء أعلاه وقم بإصلاحها');
}

console.log('\n📖 للمزيد من المعلومات، راجع ملف CONFIGURATION_GUIDE.md');
