// سكريبت لمسح التخزين المؤقت في المتصفح
// يجب تشغيل هذا الكود في Console المتصفح (F12 -> Console)

console.log("🧹 بدء مسح التخزين المؤقت...");

// 1. مسح جميع بيانات Local Storage
console.log("📦 مسح Local Storage...");
const keysToRemove = [];
for (let i = 0; i < localStorage.length; i++) {
  const key = localStorage.key(i);
  if (key) {
    keysToRemove.push(key);
  }
}

keysToRemove.forEach(key => {
  localStorage.removeItem(key);
  console.log(`✅ تم مسح: ${key}`);
});

// 2. مسح Session Storage أيضاً
console.log("🗂️ مسح Session Storage...");
sessionStorage.clear();

// 3. مسح Cache API إذا كان متاحاً
if ('caches' in window) {
  console.log("💾 مسح Cache API...");
  caches.keys().then(cacheNames => {
    return Promise.all(
      cacheNames.map(cacheName => {
        console.log(`🗑️ مسح cache: ${cacheName}`);
        return caches.delete(cacheName);
      })
    );
  }).then(() => {
    console.log("✅ تم مسح جميع caches");
  });
}

// 4. مسح IndexedDB إذا كان متاحاً
if ('indexedDB' in window) {
  console.log("🗄️ مسح IndexedDB...");
  // هذا يتطلب معرفة أسماء قواعد البيانات المحددة
  // لكن معظم التطبيقات لا تستخدمها للتخزين المؤقت البسيط
}

console.log("🎉 تم مسح التخزين المؤقت بنجاح!");
console.log("🔄 سيتم إعادة تحميل الصفحة الآن...");

// 5. إعادة تحميل الصفحة لتطبيق التغييرات
setTimeout(() => {
  location.reload();
}, 1000);

// معلومات إضافية للمطور
console.log(`
📋 ملخص العملية:
- تم مسح ${keysToRemove.length} عنصر من Local Storage
- تم مسح Session Storage
- تم مسح Cache API (إن وجد)
- سيتم إعادة تحميل الصفحة خلال ثانية واحدة

🔧 إذا لم يظهر الترتيب الصحيح بعد إعادة التحميل:
1. تأكد من تنفيذ السكريبت في Supabase أولاً
2. تحقق من Console للتأكد من عدم وجود أخطاء
3. جرب Hard Refresh (Ctrl+Shift+R أو Cmd+Shift+R)
`);
